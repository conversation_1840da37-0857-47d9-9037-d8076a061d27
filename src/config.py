"""
Configuration module for vector stores and other shared resources
Fetches configuration from tenant settings instead of hardcoding
"""

import os
from typing import Optional, Dict, Any
from dotenv import load_dotenv
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from core.database import get_db_from_tenant_id

load_dotenv()


class TenantVectorStoreManager:
    """
    Manages vector stores and embeddings for a specific tenant
    Fetches configuration from tenant's settings collection
    """

    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self._embeddings: Optional[OpenAIEmbeddings] = None
        self._info_retriever = None
        self._product_retriever = None
        self._qdrant_client: Optional[QdrantClient] = None
        self._tenant_config: Optional[Dict[str, Any]] = None

    def _get_tenant_config(self) -> Dict[str, Any]:
        """Get tenant configuration from database"""
        if self._tenant_config is None:
            try:
                tenant_db = get_db_from_tenant_id(self.tenant_id)
                env_settings = tenant_db.settings.find_one({"name": "env"})
                self._tenant_config = env_settings if env_settings else {}
            except Exception as e:
                print(f"Warning: Could not fetch tenant config: {e}")
                self._tenant_config = {}
        return self._tenant_config

    @property
    def embeddings(self) -> OpenAIEmbeddings:
        """Get or create embeddings instance"""
        if self._embeddings is None:
            config = self._get_tenant_config()
            openai_config = config.get("openai_config", {})

            self._embeddings = OpenAIEmbeddings(
                model=openai_config.get("embedding_model", "text-embedding-3-large"),
                dimensions=openai_config.get("embedding_dimensions", 1536),
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )
        return self._embeddings

    @property
    def qdrant_client(self) -> QdrantClient:
        """Get or create Qdrant client"""
        if self._qdrant_client is None:
            config = self._get_tenant_config()
            qdrant_config = config.get("qdrant_config", {})

            qdrant_url = qdrant_config.get("qdrant_url", os.getenv("QDRANT_URL", "http://localhost:6333"))
            qdrant_api_key = qdrant_config.get("qdrant_api_key", os.getenv("QDRANT_API_KEY"))

            if qdrant_api_key:
                self._qdrant_client = QdrantClient(
                    url=qdrant_url,
                    api_key=qdrant_api_key
                )
            else:
                self._qdrant_client = QdrantClient(url=qdrant_url)
        return self._qdrant_client

    def get_info_retriever(self):
        """Get information retriever"""
        if self._info_retriever is None:
            try:
                config = self._get_tenant_config()
                qdrant_config = config.get("qdrant_config", {})
                info_collection = qdrant_config.get("info_collection", "information")

                vector_store = QdrantVectorStore(
                    client=self.qdrant_client,
                    collection_name=info_collection,
                    embedding=self.embeddings
                )
                self._info_retriever = vector_store.as_retriever(
                    search_type="similarity",
                    search_kwargs={"k": 5}
                )
            except Exception as e:
                print(f"Warning: Could not initialize info retriever: {e}")
                self._info_retriever = None
        return self._info_retriever

    def get_product_retriever(self):
        """Get product retriever"""
        if self._product_retriever is None:
            try:
                config = self._get_tenant_config()
                qdrant_config = config.get("qdrant_config", {})
                product_collection = qdrant_config.get("product_collection", "products")

                vector_store = QdrantVectorStore(
                    client=self.qdrant_client,
                    collection_name=product_collection,
                    embedding=self.embeddings
                )
                self._product_retriever = vector_store.as_retriever(
                    search_type="similarity",
                    search_kwargs={"k": 5}
                )
            except Exception as e:
                print(f"Warning: Could not initialize product retriever: {e}")
                # Create mock retriever for testing
                self._product_retriever = MockProductRetriever()
        return self._product_retriever

    def search_products(self, query: str) -> str:
        """
        Search for products using vector store

        Args:
            query: Search query

        Returns:
            Formatted search results with product codes from metadata
        """
        try:
            retriever = self.get_product_retriever()
            if retriever is None:
                return "Product search is currently unavailable. Please try again later."

            # Get relevant documents
            docs = retriever.invoke(query)

            if not docs:
                return f"No products found matching '{query}'. Please try different search terms or contact support for assistance."

            # Format results with metadata including product codes
            results = []
            for i, doc in enumerate(docs, 1):
                content = doc.page_content
                metadata = doc.metadata

                # Log metadata for debugging
                print(f"🔍 Source node {i} metadata: {metadata}")

                # Extract product code from metadata
                product_code = metadata.get('code', metadata.get('product_code', 'N/A'))
                product_name = metadata.get('name', metadata.get('title', 'Unknown Product'))

                result_text = f"{i}. **{product_name}**"
                if product_code != 'N/A':
                    result_text += f" (Code: {product_code})"
                result_text += f"\n   {content}\n"

                results.append(result_text)

            formatted_results = "\n".join(results)
            return f"🎓 **Available Courses/Products:**\n\n{formatted_results}\n💡 To book any course, please let me know which one interests you!"

        except Exception as e:
            print(f"Error in product search: {e}")
            return "I'm having trouble searching for products right now. Please try again or contact support."

    def search_information(self, query: str) -> str:
        """
        Search for information using vector store

        Args:
            query: Search query

        Returns:
            Formatted search results
        """
        try:
            retriever = self.get_info_retriever()
            if retriever is None:
                return "Information search is currently unavailable. Please try again later."

            # Get relevant documents
            docs = retriever.invoke(query)

            if not docs:
                return f"No information found for '{query}'. Please try different search terms or contact our support team for assistance."

            # Format results
            results = []
            for i, doc in enumerate(docs, 1):
                content = doc.page_content
                metadata = doc.metadata

                title = metadata.get('title', metadata.get('name', f'Information {i}'))
                result_text = f"{i}. **{title}**\n   {content}\n"
                results.append(result_text)

            formatted_results = "\n".join(results)
            return f"📋 **Information Results:**\n\n{formatted_results}\n💡 Need more help? Feel free to ask!"

        except Exception as e:
            print(f"Error in information search: {e}")
            return "I'm having trouble searching for information right now. Please try again or contact support."


# Global instances per tenant
_tenant_managers: Dict[str, TenantVectorStoreManager] = {}


def get_vector_store_manager(tenant_id: str = None) -> TenantVectorStoreManager:
    """
    Get the vector store manager instance for a specific tenant
    Creates one if it doesn't exist
    """
    if tenant_id is None:
        # Default tenant for backward compatibility
        tenant_id = "default"

    global _tenant_managers
    if tenant_id not in _tenant_managers:
        _tenant_managers[tenant_id] = TenantVectorStoreManager(tenant_id)
    return _tenant_managers[tenant_id]


# Convenience functions for backward compatibility
def get_info_retriever(tenant_id: str = None):
    """Get information retriever for tenant"""
    return get_vector_store_manager(tenant_id).get_info_retriever()


def get_product_retriever(tenant_id: str = None):
    """Get product retriever for tenant"""
    return get_vector_store_manager(tenant_id).get_product_retriever()


def get_embeddings(tenant_id: str = None):
    """Get embeddings instance for tenant"""
    return get_vector_store_manager(tenant_id).embeddings
