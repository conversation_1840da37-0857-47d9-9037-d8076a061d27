#!/usr/bin/env python3
"""
Test script for dynamic search and booking agents
Tests the new vector store integration and dynamic course verification
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.services.agent_v2.search_agent import SearchAgentV2
from api.services.agent_v2.smart_booking_agent import SmartBookingAgent
from config import get_vector_store_manager

def test_vector_store_manager():
    """Test the vector store manager directly"""
    print("🔧 TESTING VECTOR STORE MANAGER")
    print("=" * 50)
    
    try:
        # Test with default tenant
        vector_manager = get_vector_store_manager("ambition-guru")
        
        # Test product search
        print("\n📚 Testing product search:")
        result = vector_manager.search_products("IELTS course")
        print(f"Result: {result[:200]}...")
        
        # Test information search
        print("\n📋 Testing information search:")
        result = vector_manager.search_information("how to contact support")
        print(f"Result: {result[:200]}...")
        
    except Exception as e:
        print(f"❌ Vector store test failed: {e}")

def test_search_agent():
    """Test the updated search agent"""
    print("\n🔍 TESTING SEARCH AGENT V2")
    print("=" * 50)
    
    try:
        search_agent = SearchAgentV2("ambition-guru")
        
        # Test product search
        print("\n📚 Testing product search:")
        result = search_agent.search_products("show me available courses", "test_thread")
        print(f"Result: {result}")
        
        # Test specific course search
        print("\n📚 Testing specific course search:")
        result = search_agent.search_products("IELTS preparation course", "test_thread")
        print(f"Result: {result}")
        
        # Test information search
        print("\n📋 Testing information search:")
        result = search_agent.search_information("how to contact support", "test_thread")
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"❌ Search agent test failed: {e}")

def test_booking_agent():
    """Test the updated booking agent"""
    print("\n📅 TESTING SMART BOOKING AGENT")
    print("=" * 50)
    
    try:
        booking_agent = SmartBookingAgent()
        
        # Test course verification
        print("\n✅ Testing course verification:")
        verification = booking_agent._verify_course_exists("IELTS course")
        print(f"Verification result: {verification}")
        
        # Test course code extraction
        print("\n🔢 Testing course code extraction:")
        code = booking_agent._extract_course_code("IELTS preparation")
        print(f"Extracted code: {code}")
        
        # Test booking flow with real course
        print("\n📝 Testing booking flow:")
        result = booking_agent.handle_booking_request("I want to book IELTS course", "test_booking")
        print(f"Booking result: {result}")
        
        # Test booking flow with fake course
        print("\n❌ Testing booking with non-existent course:")
        result = booking_agent.handle_booking_request("I want to book Quantum Physics course", "test_booking2")
        print(f"Booking result: {result}")
        
    except Exception as e:
        print(f"❌ Booking agent test failed: {e}")

def test_integration():
    """Test integration between agents"""
    print("\n🔗 TESTING AGENT INTEGRATION")
    print("=" * 50)
    
    try:
        search_agent = SearchAgentV2("ambition-guru")
        booking_agent = SmartBookingAgent()
        
        # Simulate user flow: search -> book
        print("\n1️⃣ User searches for courses:")
        search_result = search_agent.search_products("what courses do you have", "integration_test")
        print(f"Search result: {search_result[:300]}...")
        
        print("\n2️⃣ User tries to book a course:")
        booking_result = booking_agent.handle_booking_request("I want to book SEE Bridge course", "integration_test")
        print(f"Booking result: {booking_result}")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")

def main():
    """Run all tests"""
    print("🚀 TESTING DYNAMIC AGENTS")
    print("=" * 60)
    
    # Test vector store manager
    test_vector_store_manager()
    
    # Test search agent
    test_search_agent()
    
    # Test booking agent
    test_booking_agent()
    
    # Test integration
    test_integration()
    
    print("\n✅ ALL TESTS COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    main()
