#!/usr/bin/env python3
"""
Test script to verify source nodes metadata extraction
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_vector_store_source_nodes():
    """Test vector store source nodes directly"""
    print("🔧 TESTING VECTOR STORE SOURCE NODES")
    print("=" * 50)
    
    try:
        from config import get_vector_store_manager
        
        # Get vector store manager
        vector_manager = get_vector_store_manager("ambition-guru")
        
        # Test product retriever directly
        print("\n📚 Testing product retriever:")
        retriever = vector_manager.get_product_retriever()
        
        if retriever is None:
            print("❌ Product retriever is None")
            return
        
        # Search for courses
        print("\n🔍 Searching for 'IELTS course':")
        docs = retriever.invoke("IELTS course")
        
        if docs:
            print(f"✅ Found {len(docs)} documents")
            for i, doc in enumerate(docs, 1):
                print(f"\n📄 Document {i}:")
                print(f"   Content: {doc.page_content[:100]}...")
                print(f"   Metadata: {doc.metadata}")
                
                # Extract specific fields
                code = doc.metadata.get('code', doc.metadata.get('product_code', 'N/A'))
                name = doc.metadata.get('name', doc.metadata.get('title', 'N/A'))
                print(f"   Extracted - Name: {name}, Code: {code}")
        else:
            print("❌ No documents found")
            
        # Test search_products method
        print("\n🎓 Testing search_products method:")
        result = vector_manager.search_products("IELTS course")
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

def test_booking_agent_source_nodes():
    """Test booking agent source node extraction"""
    print("\n📅 TESTING BOOKING AGENT SOURCE NODES")
    print("=" * 50)
    
    try:
        from api.services.agent_v2.smart_booking_agent import SmartBookingAgent
        
        booking_agent = SmartBookingAgent()
        
        # Test course verification
        print("\n✅ Testing course verification from source nodes:")
        verification = booking_agent._verify_course_exists_from_source_nodes("IELTS course")
        print(f"Verification result: {verification}")
        
        # Test course code extraction
        print("\n🔢 Testing course code extraction from source nodes:")
        code = booking_agent._extract_course_code_from_source_nodes("IELTS preparation")
        print(f"Extracted code: {code}")
        
    except Exception as e:
        print(f"❌ Booking agent test failed: {e}")
        import traceback
        traceback.print_exc()

def test_search_agent():
    """Test search agent with vector store"""
    print("\n🔍 TESTING SEARCH AGENT")
    print("=" * 50)
    
    try:
        from api.services.agent_v2.search_agent import SearchAgentV2
        
        search_agent = SearchAgentV2("ambition-guru")
        
        # Test product search
        print("\n📚 Testing product search:")
        result = search_agent.search_products("show me available courses", "test_thread")
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"❌ Search agent test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all tests"""
    print("🚀 TESTING SOURCE NODES METADATA EXTRACTION")
    print("=" * 60)
    
    # Test vector store source nodes
    test_vector_store_source_nodes()
    
    # Test booking agent
    test_booking_agent_source_nodes()
    
    # Test search agent
    test_search_agent()
    
    print("\n✅ ALL TESTS COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    main()
