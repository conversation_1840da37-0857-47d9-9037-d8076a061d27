"""
Search Agent V2 - Pure dynamic LLM-based search with NO hardcoded data
"""

import logging
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
import os
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)


class SearchAgentV2:
    """Pure dynamic search agent using only LLM queries"""

    def __init__(self, tenant_id: str = "ambition-guru"):
        """Initialize with LLM only"""
        self.tenant_id = tenant_id
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.3,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        logger.info("✅ Search Agent V2 initialized with pure dynamic LLM search")

    def search_information(self, user_message: str, thread_id: str = "default") -> str:
        """
        Dynamic information search using LLM
        """
        logger.info(f"🔍 Information search: '{user_message}'")
        
        system_prompt = """You are a helpful customer service agent for an educational service center in Nepal.
        
The user is asking for general information, troubleshooting help, or guidance.

Provide helpful, accurate information about:
- Educational services and support
- Technical troubleshooting for apps and online platforms
- General guidance about studying in Nepal
- Information about educational systems (SEE, +2, Bachelor's, etc.)
- Study abroad guidance
- Language learning tips

Be helpful, professional, and provide practical advice. If you don't know something specific, suggest they contact support for detailed help.
"""

        try:
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"User question: {user_message}")
            ])
            
            result = response.content
            logger.info(f"✅ Information search completed")
            return result
            
        except Exception as e:
            error_msg = f"Error in information search: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble accessing information right now. Please try again or contact our support team for assistance."

    def search_products(self, user_message: str, thread_id: str = "default") -> str:
        """
        Dynamic product/course search using LLM
        """
        logger.info(f"🎓 Products search: '{user_message}'")
        
        system_prompt = """You are a course advisor for an educational service center in Nepal.

The user is asking about courses, programs, or educational services.

Based on their query, provide information about relevant educational programs such as:
- SEE Bridge courses (Grade 10 to +2 transition)
- Bachelor's programs (BBS, BBA, CSIT, etc.)
- Language courses (IELTS, Korean TOPIK, German)
- Entrance exam preparation
- Professional development courses

Guidelines:
1. If they ask about a specific course that exists in Nepal's education system, provide helpful information
2. If they ask about something that doesn't exist or isn't offered, be honest and suggest similar alternatives
3. Always be encouraging and helpful
4. Suggest they contact us for detailed course information, schedules, and enrollment

DO NOT make up specific course codes, prices, or schedules. Focus on general information and encourage them to contact for details.
"""

        try:
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"User inquiry: {user_message}")
            ])
            
            result = response.content
            logger.info(f"✅ Products search completed")
            return result
            
        except Exception as e:
            error_msg = f"Error in products search: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble accessing course information right now. Please try again or contact our support team for detailed course information."


# Global instance
_search_agent_instance = None

def get_search_agent() -> SearchAgentV2:
    """Get or create the global search agent instance"""
    global _search_agent_instance
    if _search_agent_instance is None:
        _search_agent_instance = SearchAgentV2()
    return _search_agent_instance
