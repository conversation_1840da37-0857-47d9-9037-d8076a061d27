"""
Search Agent V2 - Dynamic search using vector store with LLM fallback
"""

import logging
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
import os
from dotenv import load_dotenv
from config import get_vector_store_manager

load_dotenv()
logger = logging.getLogger(__name__)


class SearchAgentV2:
    """Dynamic search agent using vector store with LLM fallback"""

    def __init__(self, tenant_id: str = "ambition-guru"):
        """Initialize with vector store and LLM"""
        self.tenant_id = tenant_id
        self.vector_manager = get_vector_store_manager(tenant_id)
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.3,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        logger.info("✅ Search Agent V2 initialized with vector store and LLM fallback")

    def search_information(self, user_message: str, thread_id: str = "default") -> str:
        """
        Dynamic information search using vector store with LLM fallback
        """
        logger.info(f"🔍 Information search: '{user_message}'")

        try:
            # First try vector store search
            result = self.vector_manager.search_information(user_message)

            # If vector store returns generic message, use LLM fallback
            if "currently unavailable" in result.lower() or "having trouble" in result.lower():
                logger.info("📋 Vector store unavailable, using LLM fallback")
                return self._llm_information_search(user_message)

            logger.info(f"✅ Information search completed via vector store")
            return result

        except Exception as e:
            logger.warning(f"Vector store search failed: {e}, using LLM fallback")
            return self._llm_information_search(user_message)

    def _llm_information_search(self, user_message: str) -> str:
        """LLM fallback for information search"""
        system_prompt = """You are a helpful customer service agent for an educational service center in Nepal.

The user is asking for general information, troubleshooting help, or guidance.

Provide helpful, accurate information about:
- Educational services and support
- Technical troubleshooting for apps and online platforms
- General guidance about studying in Nepal
- Information about educational systems (SEE, +2, Bachelor's, etc.)
- Study abroad guidance
- Language learning tips

Be helpful, professional, and provide practical advice. If you don't know something specific, suggest they contact support for detailed help.
"""

        try:
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"User question: {user_message}")
            ])

            return response.content

        except Exception as e:
            error_msg = f"Error in information search: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble accessing information right now. Please try again or contact our support team for assistance."

    def search_products(self, user_message: str, thread_id: str = "default") -> str:
        """
        Dynamic product/course search using vector store with LLM fallback
        """
        logger.info(f"🎓 Products search: '{user_message}'")

        try:
            # First try vector store search
            result = self.vector_manager.search_products(user_message)

            # If vector store returns generic message, use LLM fallback
            if "currently unavailable" in result.lower() or "having trouble" in result.lower():
                logger.info("🎓 Vector store unavailable, using LLM fallback")
                return self._llm_products_search(user_message)

            logger.info(f"✅ Products search completed via vector store")
            return result

        except Exception as e:
            logger.warning(f"Vector store search failed: {e}, using LLM fallback")
            return self._llm_products_search(user_message)

    def _llm_products_search(self, user_message: str) -> str:
        """LLM fallback for products search"""
        system_prompt = """You are a course advisor for an educational service center in Nepal.

The user is asking about courses, programs, or educational services.

Based on their query, provide information about relevant educational programs such as:
- SEE Bridge courses (Grade 10 to +2 transition)
- Bachelor's programs (BBS, BBA, CSIT, etc.)
- Language courses (IELTS, Korean TOPIK, German)
- Entrance exam preparation
- Professional development courses

Guidelines:
1. If they ask about a specific course that exists in Nepal's education system, provide helpful information
2. If they ask about something that doesn't exist or isn't offered, be honest and suggest similar alternatives
3. Always be encouraging and helpful
4. Suggest they contact us for detailed course information, schedules, and enrollment

DO NOT make up specific course codes, prices, or schedules. Focus on general information and encourage them to contact for details.
"""

        try:
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"User inquiry: {user_message}")
            ])

            return response.content

        except Exception as e:
            error_msg = f"Error in products search: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble accessing course information right now. Please try again or contact our support team for detailed course information."


# Global instance
_search_agent_instance = None

def get_search_agent() -> SearchAgentV2:
    """Get or create the global search agent instance"""
    global _search_agent_instance
    if _search_agent_instance is None:
        _search_agent_instance = SearchAgentV2()
    return _search_agent_instance
